namespace Game.Services
{
    public interface IAnalyticsService
    {
        void Initialize();
        void SetupUser(string userId, string userName);
        void StartLevel(string levelName, string roomId, int playerCount, int gameCount);
        void EndLevel(string levelName, int playerCount, int localtime, string avatar, int fps);
        void Purchase(string title, string category, float usdSpent, int coinSpent, int coinReceived, string source);
        void BufferShouldBeNullError(string levelName, string roomId, int localTime, int serverTime);
        void MasterClientObjectEmptyError(string levelName, string roomId, int serverTime);
    }
}