using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Core;
using Game.Services;
using Game.Views.Levels;
using Game.Views.Network;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Network
{
    public class MasterClientObjectController : ControllerBase
    {
        private const int CheckMasterClientInterval = 5;

        private GameConfig gameConfig;
        private LevelModel levelModel;
        private INetworkClient networkClient;
        private IAnalyticsService analyticsService;
        private CancellationTokenSource masterClientObjectCancellationTokenSource;

        [Inject]
        private void Construct(INetworkClient networkClient, LevelModel levelModel, GameConfig gameConfig, IAnalyticsService analyticsService)
        {
            this.levelModel = levelModel;
            this.gameConfig = gameConfig;
            this.networkClient = networkClient;
            this.analyticsService = analyticsService;

            networkClient.OnPlayerJoined.Subscribe(x => HandlePlayerJoined(x.runner, x.player)).AddTo(DisposeCancellationToken);
            networkClient.IsMasterClient.Where(ok => ok).Subscribe(_ => CheckMasterClientObject()).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            masterClientObjectCancellationTokenSource.CancelAndDispose();
        }

        private void HandlePlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            if (player == runner.LocalPlayer && runner.IsSharedModeMasterClient && player.PlayerId <= 1)
            {
                runner.Spawn(gameConfig.MasterClientObjectPrefab);
            }
        }

        private void CheckMasterClientObject()
        {
            if (HasMasterClientObject())
            {
                return;
            }

            masterClientObjectCancellationTokenSource.CancelAndDispose();
            masterClientObjectCancellationTokenSource = new CancellationTokenSource();
            var token = CancellationTokenSource.CreateLinkedTokenSource(
                networkClient.DisconnectionCancellationToken,
                masterClientObjectCancellationTokenSource.Token).Token;

            UniTaskAsyncEnumerable
                .Interval(TimeSpan.FromSeconds(CheckMasterClientInterval))
                .Subscribe(_ =>
                {
                    if (HasMasterClientObject())
                    {
                        masterClientObjectCancellationTokenSource.CancelAndDispose();
                    }
                    else
                    {
                        TrackAnalytics();
                        networkClient.Spawn(gameConfig.MasterClientObjectPrefab);
                    }
                }).AddTo(token);
        }

        private bool HasMasterClientObject()
        {
            return networkClient.TryGetNetworkActorList<MasterClientObject>(out _);
        }

        private void TrackAnalytics()
        {
            var levelName = levelModel.Level.name;
            var sessionName = networkClient.SessionInfo.Name;
            var roomId = string.IsNullOrEmpty(sessionName) ? string.Empty : sessionName[..Mathf.Min(sessionName.Length, 8)];
            var serverTime = Mathf.RoundToInt(networkClient.ServerTime);
            analyticsService.MasterClientObjectEmptyError(levelName, roomId, serverTime);
        }
    }
}