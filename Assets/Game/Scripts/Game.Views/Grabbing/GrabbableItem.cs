using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Addons.Physics;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Interactors;
using VContainer;

namespace Game.Views.Grabbing
{
    [DefaultExecutionOrder(ExecutionOrder)]
    public abstract class GrabbableItem : NetworkActor, IStateAuthorityChanged
    {
        private const int ExecutionOrder = NetworkHand.ExecutionOrder + 10;
        private const float AttachThreshold = 0.01f;

        [Header("Grabbable Item")]
        [SerializeField] private XRSimpleGrabInteractable grabInteractable;
        [SerializeField] private NetworkRigidbody3D networkRigidbody;
        [SerializeField] private RigidbodySettings localGrabRigidbodySettings = new() { constraints = RigidbodyConstraints.FreezeAll };
        [SerializeField] private RigidbodySettings remoteGrabRigidbodySettings = new() { isKinematic = true };
        [SerializeField] private RigidbodySettings dropRigidbodySettings = new() { useGravity = true };
        [SerializeField] private RigidbodySettings equippedRigidbodySettings = new() { useGravity = false, isKinematic = true, constraints = RigidbodyConstraints.FreezeAll };

        private NetworkHand grabber;
        private IPublisher<GrabbableItemGrabArgs> grabPublisher;
        private CancellationTokenSource dropCancellationTokenSource;
        private CancellationTokenSource grabLocalCancellationTokenSource;

        protected int DropTimeout { get; set; } = 10;
        protected Rigidbody Rigidbody => grabInteractable.Rigidbody;
        public bool IsGrabbed => Object != null && grabber != null;
        public HandType HandType => IsGrabbed ? grabber.HandType : HandType.Left;
        public bool IsLeftInteractor => InteractorSelecting != null && InteractorSelecting.IsLeftHand();
        public bool IsRightInteractor => InteractorSelecting != null && InteractorSelecting.IsRightHand();
        public IXRSelectInteractor InteractorSelecting => grabInteractable.firstInteractorSelecting;
        public bool IsSelected => grabInteractable.isSelected;
        protected float InitialAttachEaseInTime { get; private set; }
        protected float InitialThrowVelocityScale { get; private set; }
        protected float InitialThrowAngularVelocityScale { get; private set; }

        public CancellationToken DropCancellationToken
        {
            get
            {
                dropCancellationTokenSource ??= new CancellationTokenSource();
                return dropCancellationTokenSource.Token;
            }
        }

        public float AttachEaseInTime
        {
            get => grabInteractable.attachEaseInTime;
            set => grabInteractable.attachEaseInTime = value;
        }

        public float ThrowVelocityScale
        {
            get => grabInteractable.throwVelocityScale;
            set => grabInteractable.throwVelocityScale = value;
        }

        public float ThrowAngularVelocityScale
        {
            get => grabInteractable.throwAngularVelocityScale;
            set => grabInteractable.throwAngularVelocityScale = value;
        }

        public bool ThrowOnDetach
        {
            get => grabInteractable.throwOnDetach;
            set => grabInteractable.throwOnDetach = value;
        }

        [Networked] [OnChangedRender(nameof(ChangeGrabber))]
        public NetworkBehaviourId GrabberId { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeIsAttached))]
        public NetworkBool IsAttached { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeIsEquipped))]
        public NetworkBool IsEquipped { get; set; }

        [Networked] private TickTimer DropTimer { get; set; } = TickTimer.None;

        [Inject]
        private void Construct(IPublisher<GrabbableItemGrabArgs> grabPublisher)
        {
            this.grabPublisher = grabPublisher;
        }

        protected override void Awake()
        {
            base.Awake();
            InitialAttachEaseInTime = AttachEaseInTime;
            InitialThrowVelocityScale = ThrowVelocityScale;
            InitialThrowAngularVelocityScale = ThrowAngularVelocityScale;
        }

        public override void Spawned()
        {
            base.Spawned();
            grabInteractable.hoverEntered.AddListener(HandleHoverEntered);
            grabInteractable.hoverExited.AddListener(HandleHoverExited);
            grabInteractable.BeforeSelectEntering.AddListener(HandleBeforeSelectEntering);
            grabInteractable.SelectEntering.AddListener(HandleSelectEntering);
            grabInteractable.selectEntered.AddListener(HandleSelectEntered);
            grabInteractable.BeforeSelectExiting.AddListener(HandleBeforeSelectExiting);
            grabInteractable.SelectExiting.AddListener(HandleSelectExiting);
            grabInteractable.selectExited.AddListener(HandleSelectExited);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);

            StopDropTimer();
            Ungrab();

            dropCancellationTokenSource.CancelAndDispose();
            dropCancellationTokenSource = null;

            grabLocalCancellationTokenSource.CancelAndDispose();
            grabLocalCancellationTokenSource = null;

            grabInteractable.hoverEntered.RemoveListener(HandleHoverEntered);
            grabInteractable.hoverExited.RemoveListener(HandleHoverExited);
            grabInteractable.BeforeSelectEntering.RemoveListener(HandleBeforeSelectEntering);
            grabInteractable.SelectEntering.RemoveListener(HandleSelectEntering);
            grabInteractable.selectEntered.RemoveListener(HandleSelectEntered);
            grabInteractable.BeforeSelectExiting.RemoveListener(HandleBeforeSelectExiting);
            grabInteractable.SelectExiting.RemoveListener(HandleSelectExiting);
            grabInteractable.selectExited.RemoveListener(HandleSelectExited);
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();

            if (TryDespawnByPosition())
            {
                return;
            }

            if (TryDespawnByDropTimer())
            {
            }
        }

        public override void Render()
        {
            base.Render();
            UpdateLocalAttaching();
            UpdateRemotePositionAndRotation();
        }

        public void Grab(IXRSelectInteractor interactor)
        {
            grabInteractable.Grab(interactor);
        }

        public void Ungrab()
        {
            if (IsSelected)
            {
                grabInteractable.Ungrab();
            }
            else
            {
                DropLocal();
            }
        }

        protected virtual void HandleHoverEntered(HoverEnterEventArgs args)
        {
        }

        protected virtual void HandleHoverExited(HoverExitEventArgs args)
        {
        }

        protected virtual void HandleBeforeSelectEntering(SelectEnterEventArgs args)
        {
        }

        protected virtual void HandleSelectEntering(SelectEnterEventArgs args)
        {
        }

        protected virtual void HandleSelectEntered(SelectEnterEventArgs args)
        {
            GrabLocal(args.interactorObject.IsLeftHand()).Forget();
        }

        protected virtual void HandleBeforeSelectExiting(SelectExitEventArgs args)
        {
        }

        protected virtual void HandleSelectExiting(SelectExitEventArgs args)
        {
        }

        protected virtual void HandleSelectExited(SelectExitEventArgs args)
        {
            DropLocal();
        }

        protected virtual void UpdateDropTimer(bool isStart, float dropTimeout)
        {
            if (HasStateAuthority)
            {
                DropTimer = isStart ? TickTimer.CreateFromSeconds(Runner, dropTimeout) : TickTimer.None;
            }
        }

        protected virtual void ChangeGrabber()
        {
            ChangeGrabAndEquipState();
        }

        protected virtual void ChangeIsEquipped()
        {
            ChangeGrabAndEquipState();
            ChangeAttachAndEquip();
        }

        protected virtual void ChangeIsAttached()
        {
            ChangeAttachAndEquip();
        }

        protected void SetLeftHandAttachNode(Transform node)
        {
            grabInteractable.LeftHandAttachNode = node;
        }

        protected void SetRightHandAttachNode(Transform node)
        {
            grabInteractable.RightHandAttachNode = node;
        }

        protected virtual void SetColliders(List<Collider> colliderList)
        {
            ClearColliders();
            grabInteractable.colliders.AddRange(colliderList);
        }

        protected virtual void ClearColliders()
        {
            grabInteractable.colliders.Clear();
        }

        protected void RegisterInteractable()
        {
            grabInteractable.RegisterInteractable();
        }

        protected void UnregisterInteractable()
        {
            grabInteractable.UnregisterInteractable();
        }

        void IStateAuthorityChanged.StateAuthorityChanged()
        {
            if (HasStateAuthority && IsGrabbed && !IsSelected)
            {
                DropLocal();
            }
        }

        private void ChangeGrabAndEquipState()
        {
            SetupGrabber();

            if (HasStateAuthority)
            {
                if (IsGrabbed)
                {
                    ApplyLocalGrabRigidbodySettings();
                }
                else if (IsEquipped)
                {
                    ApplyEquippedRigidbodySettings();
                    IsAttached = false;
                }
                else
                {
                    ApplyDropRigidbodySettings();
                    IsAttached = false;
                }
            }
            else
            {
                Ungrab();

                if (IsGrabbed)
                {
                    ApplyRemoteGrabRigidbodySettings();
                }
                else if (IsEquipped)
                {
                    ApplyEquippedRigidbodySettings();
                }
                else
                {
                    ApplyDropRigidbodySettings();
                }
            }
        }

        private void ChangeAttachAndEquip()
        {
        }

        private void ApplyRigidbodySettings(RigidbodySettings settings)
        {
            Rigidbody.useGravity = settings.useGravity;
            Rigidbody.isKinematic = settings.isKinematic;
            Rigidbody.constraints = settings.constraints;
        }

        private bool TryDespawnByDropTimer()
        {
            if (DropTimer.Expired(Runner))
            {
                DropTimer = TickTimer.None;
                Runner.Despawn(Object);
                return true;
            }

            return false;
        }

        private bool TryDespawnByPosition()
        {
            if (!IsSelected && !IsEquipped && transform.position.y < -1 && IsValid)
            {
                Runner.Despawn(Object);
                return true;
            }

            return false;
        }

        private void SetupGrabber()
        {
            if (GrabberId == default)
            {
                grabber = null;
            }
            else if (Runner.TryFindBehaviour(GrabberId, out var obj) && obj is NetworkHand networkHand)
            {
                grabber = networkHand;
            }
        }

        private void StopDropTimer()
        {
            UpdateDropTimer(false, 0);
        }

        private void ApplyLocalGrabRigidbodySettings()
        {
            ApplyRigidbodySettings(localGrabRigidbodySettings);
        }

        private void ApplyRemoteGrabRigidbodySettings()
        {
            ApplyRigidbodySettings(remoteGrabRigidbodySettings);
        }

        private void ApplyDropRigidbodySettings()
        {
            ApplyRigidbodySettings(dropRigidbodySettings);
        }

        private void ApplyEquippedRigidbodySettings()
        {
            ApplyRigidbodySettings(equippedRigidbodySettings);
        }

        private async UniTaskVoid GrabLocal(bool isLeftHand)
        {
            ApplyLocalGrabRigidbodySettings();

            if (!HasStateAuthority)
            {
                grabLocalCancellationTokenSource.CancelAndDispose();
                grabLocalCancellationTokenSource = new CancellationTokenSource();
                await Object.RequestStateAuthorityAsync(grabLocalCancellationTokenSource.Token);
            }

            if (HasStateAuthority && IsSelected)
            {
                grabPublisher.Publish(new GrabbableItemGrabArgs(this, isLeftHand));
            }
        }

        private void DropLocal()
        {
            grabLocalCancellationTokenSource.CancelAndDispose();
            grabLocalCancellationTokenSource = null;

            dropCancellationTokenSource.CancelAndDispose();
            dropCancellationTokenSource = null;

            ApplyDropRigidbodySettings();

            if (HasStateAuthority && GrabberId != default)
            {
                GrabberId = default;
            }
        }

        private void SetActiveNetworkRigidbody(bool isActive)
        {
            if (networkRigidbody.enabled == isActive)
            {
                return;
            }

            networkRigidbody.enabled = isActive;
        }

        private void UpdateLocalAttaching()
        {
            if (!HasStateAuthority || !IsGrabbed)
            {
                return;
            }

            if (!IsAttached && CanAttach())
            {
                IsAttached = true;
            }
        }

        private void UpdateRemotePositionAndRotation()
        {
            if (HasStateAuthority || !IsGrabbed || !IsAttached)
            {
                return;
            }

            var grabbableAttachNode = GetGrabbableAttachNode();
            if (grabbableAttachNode == null)
            {
                return;
            }

            var rotation = grabber.AttachNode.rotation * Quaternion.Inverse(grabbableAttachNode.localRotation);
            var position = grabber.AttachNode.position - rotation * transform.InverseTransformPoint(grabbableAttachNode.position);
            transform.SetPositionAndRotation(position, rotation);
        }

        private bool CanAttach()
        {
            var attachNode = GetGrabbableAttachNode();
            if (attachNode == null)
            {
                return false;
            }

            return (grabber.AttachNode.position - attachNode.position).sqrMagnitude < AttachThreshold;
        }

        private Transform GetGrabbableAttachNode()
        {
            return grabber == null ? null : grabber.HandType == HandType.Left ? grabInteractable.LeftHandAttachNode : grabInteractable.RightHandAttachNode;
        }

        [Serializable]
        public class RigidbodySettings
        {
            public bool useGravity;
            public bool isKinematic;
            public RigidbodyConstraints constraints;
        }
    }
}