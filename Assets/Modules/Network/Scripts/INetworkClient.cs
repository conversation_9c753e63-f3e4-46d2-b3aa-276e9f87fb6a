using System;
using System.Collections.Generic;
using System.Reactive;
using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Sockets;
using UnityEngine;

namespace Modules.Network
{
    public interface INetworkClient
    {
        PlayerRef LocalPlayer { get; }
        float LocalTime { get; }
        float ServerTime { get; }
        IEnumerable<PlayerRef> ActivePlayers { get; }
        LobbyInfo LobbyInfo { get; }
        SessionInfo SessionInfo { get; }
        IReadOnlyAsyncReactiveProperty<bool> IsMasterClient { get; }
        IReadOnlyAsyncReactiveProperty<int> PlayerCount { get; }
        IReadOnlyAsyncReactiveProperty<bool> IsConnected { get; }
        CancellationToken DisconnectionCancellationToken { get; }
        IObservable<(NetworkRunner runner, PlayerRef player)> OnPlayerJoined { get; }
        IObservable<(NetworkRunner runner, PlayerRef player)> OnPlayerLeft { get; }
        IObservable<(NetworkRunner runner, ShutdownReason reason)> OnShutdown { get; }
        IObservable<NetworkRunner> OnConnectedToServer { get; }
        IObservable<(NetworkRunner runner, NetDisconnectReason reason)> OnDisconnectedFromServer { get; }
        IObservable<(NetworkRunner runner, List<SessionInfo> sessionList)> OnSessionListUpdated { get; }
        IObservable<(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)> OnReliableDataReceived { get; }
        IObservable<(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)> OnReliableDataProgress { get; }
        IObservable<(NetworkRunner runner, NetworkActor actor)> OnNetworkActorSpawned { get; }
        IObservable<(NetworkRunner runner, NetworkActor actor)> OnNetworkActorDespawned { get; }
        IObservable<Unit> OnRunnerDestroyed { get; }
        IObservable<NetworkRunner> OnReadyMasterObjectSpawn { get; }
        UniTask<StartGameResult> ConnectSession(ConnectSessionArgs connectSessionArgs, CancellationToken cancellationToken = default);
        UniTask Disconnect(ShutdownReason reason = ShutdownReason.Ok, CancellationToken cancellationToken = default);
        void Spawn(NetworkObject prefab, Vector3? position = null, Quaternion? rotation = null, NetworkRunner.OnBeforeSpawned onBeforeSpawned = null);
        void Despawn(NetworkObject networkObject);
        void SendReliableDataToPlayer(PlayerRef playerRef, ReliableKey reliableKey, byte[] data);
        bool TryGetNetworkActorList<T>(out List<T> result) where T : NetworkActor;
        bool HasNetworkActor<T>() where T : NetworkActor;
        bool HasPlayer(PlayerRef playerRef);
        bool HasPlayer(int playerId);
    }
}