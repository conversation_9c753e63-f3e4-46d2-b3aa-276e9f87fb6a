using System;
using System.Threading;
using Fusion;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Modules.Network
{
    public class NetworkActor : NetworkBehaviour, IAfterSpawned
    {
        [Header("Network Actor")]
        [SerializeField] private InjectionType injectionType;

        private bool isInjected;
        private CancellationTokenSource despawnCancellationTokenSource;

        public PlayerRef StateAuthority => Object == null ? PlayerRef.None : Object.StateAuthority;
        public int PlayerId => Object == null ? -1 : Object.StateAuthority.PlayerId;
        public bool IsValid => Object != null && Object.IsValid && Runner != null && !Runner.IsShutdown;

        public CancellationToken DespawnCancellationToken
        {
            get
            {
                despawnCancellationTokenSource ??= new CancellationTokenSource();
                return despawnCancellationTokenSource.Token;
            }
        }

        [Inject]
        private void ConstructInternal()
        {
            if (isInjected)
            {
                Logger.Network.Error("NetworkActor [{0}] already injected", GetType().Name);
            }

            isInjected = true;
        }

        protected virtual void Awake()
        {
            NetworkActorDispatcher.Inject(injectionType, this);
        }

        public virtual void AfterSpawned()
        {
            NetworkActorDispatcher.Spawned(this);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            despawnCancellationTokenSource.CancelAndDispose();
            despawnCancellationTokenSource = null;

            NetworkActorDispatcher.Despawned(this);
        }

        public virtual void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        protected void SendRpcSafe(Action callback)
        {
            if (!IsValid)
            {
                return;
            }

            callback?.Invoke();
        }
    }
}